<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>BusRouter SG - Map View</title>
    <link rel="stylesheet" href="npm:maplibre-gl/dist/maplibre-gl.css" />
    <style>
      body {
        margin: 0;
        padding: 0;
      }
      #map {
        width: 400px;
        height: 200px;
      }
      .maplibregl-ctrl-group,
      .maplibregl-ctrl.maplibregl-ctrl-attrib {
        display: none !important;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>
    <script type="module">
      import maplibregl from 'maplibre-gl';
      import { Protocol } from 'pmtiles';
      import { layers, namedFlavor } from '@protomaps/basemaps';

      const urlParams = new URLSearchParams(window.location.search);
      const lng = parseFloat(urlParams.get('lng')) || 103.8198;
      const lat = parseFloat(urlParams.get('lat')) || 1.3521;

      let protocol = new Protocol();
      maplibregl.addProtocol('pmtiles', protocol.tile);

      const sgTilesPath = new URL('../tiles/singapore.pmtiles', import.meta.url);
      const flavor = namedFlavor('light');
      const mapLayers = layers('protomaps', flavor, { lang: 'en' });

      const mapStyle = {
        version: 8,
        glyphs: 'https://protomaps.github.io/basemaps-assets/fonts/{fontstack}/{range}.pbf',
        sprite: 'https://protomaps.github.io/basemaps-assets/sprites/v4/light',
        sources: {
          protomaps: {
            type: 'vector',
            url: `pmtiles://${sgTilesPath}`,
            attribution: '<a href="https://protomaps.com" target="_blank">Protomaps</a> © <a href="https://openstreetmap.org" target="_blank">OpenStreetMap</a>',
          },
        },
        layers: mapLayers,
      };

      const map = new maplibregl.Map({
        container: 'map',
        style: mapStyle,
        center: [lng, lat],
        zoom: 17,
        pitch: 60,
        interactive: false,
        attributionControl: false,
      });
    </script>
  </body>
</html>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>BusRouter SG - Map View</title>
    <link rel="stylesheet" href="../node_modules/maplibre-gl/dist/maplibre-gl.css" />
    <style>
      body {
        margin: 0;
        padding: 0;
      }
      #map {
        width: 400px;
        height: 200px;
      }
      .maplibregl-ctrl-group,
      .maplibregl-ctrl.maplibregl-ctrl-attrib {
        display: none !important;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>
    <script type="module">
      import maplibregl from '../node_modules/maplibre-gl/dist/maplibre-gl.js';
      import { Protocol } from '../node_modules/pmtiles/dist/index.js';
      import { createMapStyle } from '../assets/map-style.js';

      const urlParams = new URLSearchParams(window.location.search);
      const lng = parseFloat(urlParams.get('lng')) || 103.8198;
      const lat = parseFloat(urlParams.get('lat')) || 1.3521;

      let protocol = new Protocol();
      maplibregl.addProtocol('pmtiles', protocol.tile);

      const mapStyle = createMapStyle({ lang: 'en' });

      const map = new maplibregl.Map({
        container: 'map',
        style: mapStyle,
        center: [lng, lat],
        zoom: 17,
        pitch: 60,
        interactive: false,
        attributionControl: false,
      });
    </script>
  </body>
</html>

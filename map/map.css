@import 'npm:maplibre-gl/dist/maplibre-gl.css';

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#map {
  width: 800px;
  height: 400px;
  margin: 0 auto;
  border: 1px solid #ccc;
  background-color: #f9f5ed;
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.maplibregl-canvas {
  outline: 0;
}

/* Hide attribution and controls */
.maplibregl-ctrl-group {
  display: none !important;
}

.maplibregl-ctrl.maplibregl-ctrl-attrib {
  display: none !important;
}

.maplibregl-ctrl-bottom-left {
  display: none !important;
}

.maplibregl-ctrl-bottom-right {
  display: none !important;
}

.maplibregl-ctrl-top-left {
  display: none !important;
}

.maplibregl-ctrl-top-right {
  display: none !important;
}

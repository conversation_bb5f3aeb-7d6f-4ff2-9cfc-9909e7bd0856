import maplibregl from 'maplibre-gl';
import { Protocol } from 'pmtiles';
import { createMapStyle } from '../assets/map-style.js';

// Get URL parameters
const urlParams = new URLSearchParams(window.location.search);
const lng = parseFloat(urlParams.get('lng')) || 103.8198;
const lat = parseFloat(urlParams.get('lat')) || 1.3521;

// Set up PMTiles protocol
let protocol = new Protocol();
maplibregl.addProtocol('pmtiles', protocol.tile);

// Create map style
const mapStyle = createMapStyle({ lang: 'en' });

// Initialize map
const map = new maplibregl.Map({
  container: 'map',
  style: mapStyle,
  center: [lng, lat],
  zoom: 17,
  pitch: 60,
  interactive: false,
  attributionControl: false, // Hide attribution
});

// Hide navigation controls
// No controls are added, so they're hidden by default

// Wait for map to load
map.on('load', () => {
  console.log('Map loaded successfully');

  // Add rail lines layer
  const layers = map.getStyle().layers;
  const labelLayerId = layers.find(
    (l) => l.type == 'symbol' && l.layout['text-field'],
  ).id;

  // Add rail lines layer
  map.addLayer(
    {
      id: 'rail-path',
      type: 'line',
      source: 'sg-rail',
      filter: [
        'all',
        ['==', ['geometry-type'], 'LineString'],
        ['has', 'line_color'],
      ],
      minzoom: 12.5,
      layout: {
        'line-join': 'round',
        'line-cap': 'round',
      },
      paint: {
        'line-color': ['to-color', ['get', 'line_color']],
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          12,
          0.5,
          16,
          1.5,
          22,
          2,
        ],
        'line-opacity': 0.5,
      },
    },
    labelLayerId,
  );

  map.addLayer(
    {
      id: 'rail-path-case',
      type: 'line',
      source: 'sg-rail',
      filter: [
        'all',
        ['==', ['geometry-type'], 'LineString'],
        ['has', 'line_color'],
      ],
      minzoom: 13,
      layout: {
        'line-join': 'round',
        'line-cap': 'round',
      },
      paint: {
        'line-color': '#fff',
        'line-width': ['interpolate', ['linear'], ['zoom'], 16, 4.5, 22, 6],
        'line-opacity': 0.5,
      },
    },
    'rail-path',
  );

  map.addLayer({
    id: 'rail-path-label',
    type: 'symbol',
    source: 'sg-rail',
    filter: [
      'all',
      ['==', ['geometry-type'], 'LineString'],
      ['has', 'line_color'],
    ],
    minzoom: 13,
    layout: {
      'symbol-placement': 'line',
      'symbol-spacing': 300,
      'text-field': ['get', 'name'],
      'text-font': ['Noto Sans Regular'],
      'text-size': 13,
    },
    paint: {
      'text-color': ['to-color', ['get', 'line_color']],
      'text-halo-color': '#fff',
      'text-halo-width': 2,
    },
  });

  // Add rail stations layer
  map.addLayer({
    id: 'rail-stations',
    type: 'symbol',
    source: 'sg-rail',
    filter: [
      'all',
      ['==', ['geometry-type'], 'Point'],
      ['==', ['get', 'stop_type'], 'station'],
    ],
    minzoom: 13,
    layout: {
      'icon-image': [
        'case',
        ['==', ['get', 'network'], 'singapore-lrt'],
        'lrt-station',
        'mrt-station',
      ],
      'icon-size': 0.2,
      'icon-allow-overlap': false,
      'text-field': ['coalesce', ['get', 'name_en'], ['get', 'name']],
      'text-font': ['Noto Sans Medium'],
      'text-size': ['interpolate', ['linear'], ['zoom'], 0, 0, 22, 16],
      'text-variable-anchor': ['left', 'right', 'top'],
      'text-radial-offset': 0.85,
      'text-optional': true,
    },
    paint: {
      'text-color': '#4d787e',
      'text-halo-color': '#fff',
      'text-halo-width': 1,
    },
  });
});

// Load station images
import mrtStationImagePath from '../assets/images/mrt-station.png';
import lrtStationImagePath from '../assets/images/lrt-station.png';

map
  .loadImage(mrtStationImagePath)
  .then((img) => {
    map.addImage('mrt-station', img.data);
  })
  .catch((e) => {
    console.error('Failed to load mrt-station image:', e);
  });

map
  .loadImage(lrtStationImagePath)
  .then((img) => {
    map.addImage('lrt-station', img.data);
  })
  .catch((e) => {
    console.error('Failed to load lrt-station image:', e);
  });

// Handle map errors gracefully
map.on('error', (e) => {
  console.warn('Map error:', e.error);
});

console.log(`Map initialized at coordinates: ${lng}, ${lat}`);
